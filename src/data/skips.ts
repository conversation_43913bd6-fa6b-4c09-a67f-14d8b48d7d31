import { SkipSize } from '../types';

export const skipSizes: SkipSize[] = [
  {
    id: '2yd',
    name: 'Mini Skip – 2 Yard',
    price: 150.00,
    dimensions: '6ft x 4ft x 3ft 4ins',
    capacity: '20-30 bin bags',
    suitableFor: [
      'Small garden waste',
      'Home clearance',
      'Minor renovation work'
    ],
    imageUrl: '/assets/images/skipsizes/2yd.jpg' 
  },
  {
    id: '4yd',
    name: 'Midi Skip – 4 Yard',
    price: 220.00,
    dimensions: '7ft 8ins x 4ft 6ins x 4ft',
    capacity: '40-50 bin bags',
    suitableFor: [
      'Small home renovations',
      'Garden clearance',
      'Kitchen refits'
    ],
    imageUrl: '/assets/images/skipsizes/4yd.jpg'
  },
  {
    id: '8yd',
    name: '<PERSON><PERSON> Ski<PERSON> – 8 Yard',
    price: 320.00,
    dimensions: '11ft 6ins x 5ft 6ins x 4ft 6ins',
    capacity: '80-100 bin bags',
    suitableFor: [
      'House clearance',
      'Building projects',
      'Commercial waste'
    ],
    imageUrl: '/assets/images/skipsizes/8yd.jpg'
  },
  {
    id: '12yd',
    name: '12 Yard Skip',
    price: 480.00,
    dimensions: '12ft x 6ft x 5ft 5ins',
    capacity: '120-140 bin bags',
    suitableFor: [
      'Large construction',
      'Factory clearance',
      'Industrial waste'
    ],
    imageUrl: '/assets/images/skipsizes/12yd.jpg'
  },
  {
    id: '14yd',
    name: '14 Yard Lockable Skip',
    price: 520.00,
    dimensions: '12ft x 5.75ft x 6ft',
    capacity: '140-160 bin bags',
    suitableFor: [
      'Secure waste storage',
      'Large commercial projects',
      'Construction sites'
    ],
    imageUrl: '/assets/images/skipsizes/14yd.jpg'
  }
];