interface PriceZone {
    name: string;
    postcodes: string[];
    permitCost: number;
    skipPrices: {
      [key: string]: number;
    };
  }

  export const priceZones: PriceZone[] = [
    {
      name: 'Manchester',
      postcodes: [
        'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9', 'M10',
        'M11', 'M12', 'M13', 'M14', 'M15', 'M16', 'M17', 'M18', 'M19', 'M20',
        'M21', 'M22', 'M23', 'M24', 'M25', 'M26', 'M27', 'M28', 'M29', 'M30',
        'M31', 'M32', 'M33', 'M34', 'M35', 'M36', 'M37', 'M38', 'M39', 'M40',
        'M41', 'M42', 'M43', 'M44', 'M45', 'M46', 'M60', 'M90'
      ],
      permitCost: 55,
      skipPrices: {
        '2yd': 170,
        '4yd': 230,
        '8yd': 340,
        '14yd': 500
      }
    },
    {
      name: '<PERSON><PERSON>',
      postcodes: [
        'SK1', 'SK2', 'SK3', 'SK4', 'SK5', 'SK6', 'SK7', 'SK8',
        'SK9', 'SK10', 'SK11', 'SK12', 'SK13', 'SK14', 'SK15', 'SK16'
      ],
      permitCost: 80,
      skipPrices: {
        '2yd': 170,
        '4yd': 230,
        '8yd': 340,
        '14yd': 500
      }
    },
    {
      name: 'Oldham',
      postcodes: [
        'OL1', 'OL2', 'OL3', 'OL4', 'OL5', 'OL6', 'OL7', 'OL8',
        'OL9', 'OL10', 'OL11', 'OL12', 'OL13', 'OL14', 'OL15', 'OL16'
      ],
      permitCost: 80,
      skipPrices: {
        '2yd': 170,
        '4yd': 230,
        '8yd': 340,
        '14yd': 500
      }
    },
    {
      name: 'Bury and Bolton',
      postcodes: [
        'BL0', 'BL1', 'BL2', 'BL3', 'BL4', 'BL5', 'BL6', 'BL7', 'BL8', 'BL9'
      ],
      permitCost: 80,
      skipPrices: {
        '2yd': 170,
        '4yd': 230,
        '8yd': 340,
        '14yd': 500
      }
    }
  ];

  export const isUKBankHoliday = (date: Date): boolean => {
    // Basic UK bank holidays (you may want to expand this list)
    const bankHolidays = [
      '2024-01-01', // New Year's Day
      '2024-03-29', // Good Friday
      '2024-04-01', // Easter Monday
      '2024-05-06', // Early May Bank Holiday
      '2024-05-27', // Spring Bank Holiday
      '2024-08-26', // Summer Bank Holiday
      '2024-12-25', // Christmas Day
      '2024-12-26', // Boxing Day
    ];

    const dateString = date.toISOString().split('T')[0];
    return bankHolidays.includes(dateString);
  };

  export const isValidDeliveryDate = (date: Date): boolean => {
    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6; // 0 is Sunday, 6 is Saturday

    // Check if it's a bank holiday
    if (isUKBankHoliday(date)) {
      return false;
    }

    // Check if it's a weekend
    if (isWeekend) {
      return false;
    }

    // Check if it's at least 48 hours from now
    const minDate = new Date();
    minDate.setHours(minDate.getHours() + 48);
    return date >= minDate;
  };

  export const getPriceForPostcode = (postcode: string): PriceZone | null => {
    // Normalize the postcode by removing spaces and converting to uppercase
    const normalizedPostcode = postcode.toUpperCase().replace(/\s+/g, '');

    // Extract the outward code (the part before the numbers at the end)
    // This handles both formats: "M24 1LP" and "M241LP"
    const outwardCodeMatch = normalizedPostcode.match(/^([A-Z]+)\d/);

    if (!outwardCodeMatch) return null;

    const outwardCode = outwardCodeMatch[1];

    return priceZones.find(zone =>
      zone.postcodes.some(code => code.startsWith(outwardCode))
    ) || null;
  };