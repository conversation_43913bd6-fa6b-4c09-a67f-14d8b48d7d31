import { loadStripe } from '@stripe/stripe-js';
import type { Stripe } from '@stripe/stripe-js';

let stripePromise: Promise<Stripe | null>;

// Try to get the Stripe key from different sources
const getStripeKey = (): string => {
  // Check if we have a global config object (for runtime configuration)
  if (typeof window !== 'undefined' && window.APP_CONFIG && window.APP_CONFIG.STRIPE_PUBLIC_KEY) {
    return window.APP_CONFIG.STRIPE_PUBLIC_KEY;
  }

  // Fall back to build-time environment variable
  return import.meta.env.VITE_STRIPE_PUBLIC_KEY;
};

export const getStripe = () => {
  if (!stripePromise) {
    const stripeKey = getStripeKey();
    if (!stripeKey) {
      console.error('Stripe public key is missing. Please check your configuration.');
    }
    stripePromise = loadStripe(stripeKey);
  }
  return stripePromise;
};

interface CheckoutOptions {
  skipSize: string;
  price: number;
  customerEmail?: string;
  startDate: string;
  endDate: string;
}

export const redirectToCheckout = async (options: CheckoutOptions) => {
  try {
    // Get the Stripe instance
    const stripe = await getStripe();
    if (!stripe) throw new Error('Stripe failed to load');

    // The price already includes the extra days charge from the QuoteModal component
    const totalAmount = options.price;

    // Create a checkout session using our PHP bridge
    const response = await fetch('https://nationalskiphireuk.co.uk/stripe-bridge.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: totalAmount,
        skipSize: options.skipSize,
        startDate: options.startDate,
        endDate: options.endDate,
        customerEmail: options.customerEmail,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create checkout session');
    }

    const session = await response.json();

    // Redirect to Stripe Checkout using the session ID
    const { error } = await stripe.redirectToCheckout({
      sessionId: session.id,
    });

    if (error) {
      console.error('Stripe checkout error:', error);
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Checkout error:', error);

    // Show a more user-friendly error message
    alert('We are experiencing issues with our payment system. Please try again later or contact us directly to complete your order.');
    throw error;
  }
};