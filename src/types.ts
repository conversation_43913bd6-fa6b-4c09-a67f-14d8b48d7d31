export interface SkipSize {
  id: string;
  name: string;
  price: number;
  dimensions: string;
  capacity: string;
  suitableFor: string[];
  imageUrl: string;
}

export interface ServiceArea {
  city: string;
  postcode: string;
}

export interface Testimonial {
  id: string;
  name: string;
  company?: string;
  content: string;
  rating: number;
  date: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  date: string;
  imageUrl: string;
  content?: string;
}