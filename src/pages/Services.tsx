import { useState } from 'react';
import { Check } from 'lucide-react';
import { skipSizes } from '../data/skips';
import QuoteModal from '../components/QuoteModal';

const Services = () => {
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const [selectedSkipId, setSelectedSkipId] = useState<string>();

  const handleGetQuote = (skipId: string) => {
    setSelectedSkipId(skipId);
    setIsQuoteModalOpen(true);
  };

  return (
    <div className="pt-24 pb-16">
      {/* Hero Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-6">Our Skip Hire Services</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From small domestic clearances to large commercial projects,
              we have the perfect skip size for your needs.
            </p>
          </div>
        </div>
      </section>

      {/* Skip Sizes */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {skipSizes.map((skip) => (
              <div key={skip.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <img
                  src={skip.imageUrl}
                  alt={skip.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2">{skip.name}</h3>
                  <p className="text-gray-600 mb-4">Dimensions: {skip.dimensions}</p>

                  <p className="text-gray-600 mb-4">Capacity: {skip.capacity}</p>
                  <h4 className="font-medium mb-2">Suitable for:</h4>
                  <ul className="space-y-2">
                    {skip.suitableFor.map((use, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <Check size={16} className="text-[#ffb000] mr-2" />
                        {use}
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handleGetQuote(skip.id)}
                    className="w-full mt-6 bg-[#ffb000] text-white px-6 py-2 rounded-md font-medium hover:bg-[#e69d00] transition-colors"
                  >
                    Starting from £{skip.price.toFixed(2)}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose Us</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Competitive Pricing',
                description: 'Best rates guaranteed with no hidden fees',
              },
              {
                title: 'Flexible Hire Periods',
                description: 'Keep your skip for as long as you need',
              },
              {
                title: 'Environmental Focus',
                description: 'Committed to recycling and waste reduction',
              },
              {
                title: 'Nationwide Coverage',
                description: 'Service available across the UK',
              },
              {
                title: 'Expert Advice',
                description: 'Professional guidance on skip selection',
              },
              {
                title: 'Fast Delivery',
                description: 'Next day delivery available',
              },
            ].map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      <QuoteModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
        selectedSkipId={selectedSkipId}
      />
    </div>
  );
};

export default Services;