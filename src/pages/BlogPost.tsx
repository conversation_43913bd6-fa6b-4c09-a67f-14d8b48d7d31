import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { blogPosts } from '../data/blog';
import { ArrowLeft } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

const BlogPost = () => {
  const { id } = useParams();
  const post = blogPosts.find(post => post.id === id);

  if (!post) {
    return (
      <div className="pt-24 pb-16 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4 dark:text-white">Article Not Found</h1>
            <Link to="/blog" className="text-[#ffb000] hover:text-[#e69d00]">
              ← Back to Blog
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-24 pb-16 bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <section className="relative h-96">
        <div className="absolute inset-0">
          <img
            src={post.imageUrl}
            alt={post.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-50"></div>
        </div>
        <div className="relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center">
          <div className="max-w-3xl">
            <Link
              to="/blog"
              className="inline-flex items-center text-white mb-4 hover:text-[#ffb000] transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Link>
            <h1 className="text-4xl font-bold text-white mb-4">{post.title}</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-[#ffb000] bg-[#fff3d6] px-3 py-1 rounded-full">
                {post.category}
              </span>
              <span className="text-sm text-white">
                {new Date(post.date).toLocaleDateString('en-GB', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric'
                })}
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown>{post.content || ''}</ReactMarkdown>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPost;