import { Check, Truck, FileCheck, Recycle, Clock, ChevronDown } from 'lucide-react';
import { useState } from 'react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqs: FAQItem[] = [
  {
    question: "How long can I keep the skip?",
    answer: "Standard hire period is 7 days. Extended hire is available at £10 per additional day. Please let us know in advance if you need the skip for longer."
  },
  {
    question: "Do I need a permit for the skip?",
    answer: "If the skip is to be placed on a public road, you will need a permit. Prices range from £40 to £140 depending on your local authority. No permit is required for private property placement."
  },
  {
    question: "What size skip do I need?",
    answer: "We offer skips from 2 to 14 yards. A 2-yard skip is ideal for small garden waste, while an 8-yard skip suits house renovations. Contact us for specific advice based on your project."
  },
  {
    question: "What can't I put in a skip?",
    answer: "Prohibited items include hazardous waste, asbestos, batteries, paint, gas bottles, and clinical waste. Please contact us if you're unsure about specific items."
  },
  {
    question: "How quickly can you deliver?",
    answer: "We offer next-day delivery for most areas when ordered before 12pm. Same-day delivery may be available in some locations. All deliveries require 48 hours notice and are not available on weekends or bank holidays."
  },
  {
    question: "What are the additional charges?",
    answer: "Additional charges apply for specific items: Mattresses (£25), Sofas (£25), Fridges (£25), and Plasterboard (£180). Mixed plasterboard will incur a £200 service charge if not declared when ordering."
  }
];

const AboutUs = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <div className="pt-24 pb-16 bg-white dark:bg-gray-900">
      {/* FAQ Section */}
      <section className="py-16 bg-gray-100 dark:bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-center mb-12 dark:text-white">Frequently Asked Questions</h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden"
              >
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none"
                  onClick={() => toggleFAQ(index)}
                >
                  <span className="font-medium dark:text-white">{faq.question}</span>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-500 transform transition-transform ${
                      openFAQ === index ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                <div
                  className={`px-6 pb-4 transition-all duration-200 ease-in-out ${
                    openFAQ === index ? 'block opacity-100' : 'hidden opacity-0'
                  }`}
                >
                  <p className="text-gray-600 dark:text-gray-400">{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-4 dark:text-white">Our Vision</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Our vision is to optimise the skip hire process, ensuring it is efficient, convenient and simple for customers. As we grow, our goal is to continually expand our service areas and offerings to better serve our clientele.
                </p>
              </div>

              <div>
                <h2 className="text-2xl font-bold mb-4 dark:text-white">Why Choose Us</h2>
                <ul className="grid grid-cols-1 gap-3">
                  {[
                    'Reliable and easy booking system',
                    'Friendly staff',
                    'Hassle free',
                    'Only reputable and reliable companies',
                    'Click and collect service',
                    'Online booking made simple',
                    '25 years knowledge in industry',
                    'Waste disposed of correctly only through licensed suppliers',
                    'Environmentally conscious building a sustainable Future for our children'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="w-5 h-5 text-[#ffb000] mt-1 mr-2 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-400">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-4 dark:text-white">Permits</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  All skips placed on public highways will incur a permit charge, with prices ranging from £40 to £140 depending on your local authority. These permits, which are issued by the local council, are valid for a period of 7 to 28 days.
                </p>
              </div>

              <div>
                <h2 className="text-2xl font-bold mb-4 dark:text-white">Additional Charges</h2>
                <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Certain waste types will incur an extra charge on booking if going into the skips. Please specify if you have any of these items when placing the order with our team:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Mattresses</span>
                      <span className="font-semibold text-[#ffb000]">£25</span>
                    </li>
                    <li className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Sofa</span>
                      <span className="font-semibold text-[#ffb000]">£25</span>
                    </li>
                    <li className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Fridges</span>
                      <span className="font-semibold text-[#ffb000]">£25</span>
                    </li>
                    <li className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Plasterboard skips</span>
                      <span className="font-semibold text-[#ffb000]">£180</span>
                    </li>
                  </ul>
                  <p className="text-red-500 dark:text-red-400 mt-4 text-sm">
                    Note: Any skips collected which contain mixed plasterboard will incur a £200 service charge if not stated when pre-ordering the skip.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What to Expect */}
      <section className="py-16 bg-gray-100 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center dark:text-white">What to Expect</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <FileCheck className="w-12 h-12 text-[#ffb000]" />,
                title: "Booking Your Skip",
                description: "Select your skip size, determine placement location, and contact us to book. We recommend booking in advance, especially during busy periods."
              },
              {
                icon: <Truck className="w-12 h-12 text-[#ffb000]" />,
                title: "Delivery",
                description: "We'll deliver to your designated location. Ensure clear access and necessary permits for road placement."
              },
              {
                icon: <Clock className="w-12 h-12 text-[#ffb000]" />,
                title: "Filling the Skip",
                description: "Fill evenly, don't overfill, and ensure no prohibited items are included. Safety regulations must be followed."
              },
              {
                icon: <Recycle className="w-12 h-12 text-[#ffb000]" />,
                title: "Collection",
                description: "We'll collect the skip and ensure proper waste sorting and disposal, prioritizing recycling when possible."
              }
            ].map((step, index) => (
              <div key={index} className="text-center p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg">
                <div className="flex justify-center mb-4">{step.icon}</div>
                <h3 className="text-xl font-semibold mb-3 dark:text-white">{step.title}</h3>
                <p className="text-gray-600 dark:text-gray-400">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutUs;