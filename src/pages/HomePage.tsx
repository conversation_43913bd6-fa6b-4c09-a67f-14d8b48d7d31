import { useState } from 'react';
import { Check } from 'lucide-react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../components/PostcodeChecker';
import { skipSizes } from '../data/skips';
import QuoteModal from '../components/QuoteModal';
import Map from '../components/Map';
import Features from '../components/Features';
import ContactForm from '../components/ContactForm';

const HomePage = () => {
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const [selectedSkipId, setSelectedSkipId] = useState<string>();

  const handleGetQuote = (skipId: string) => {
    setSelectedSkipId(skipId);
    setIsQuoteModalOpen(true);
  };

  return (
    <div className="flex flex-col bg-white dark:bg-gray-900 transition-colors">
      {/* Hero Section */}
      <section
        id="home"
        className="pt-32 pb-16 bg-cover bg-center"
        style={{
          backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(/assets/images/hero.jpg)',
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Affordable Skip Hire Across the UK
            </h1>
            <p className="text-xl text-gray-200 mb-8">
              Fast, reliable skip hire services for domestic and commercial projects.<br></br>
              Get an instant quote today!
            </p>
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md">
              <h2 className="text-xl font-semibold mb-4">Check Availability in Your Area</h2>
              <PostcodeChecker />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <Features />

      {/* Services Section */}
      <section id="services" className="py-16 bg-gray-100 dark:bg-gray-800 transition-colors">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 dark:text-white">Our Skip Hire Services</h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              From small domestic clearances to large commercial projects,
              we have the perfect skip size for your needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {skipSizes.map((skip) => (
              <div key={skip.id} className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden transition-colors">
                <img
                  src={skip.imageUrl}
                  alt={skip.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 dark:text-white">{skip.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">Dimensions: {skip.dimensions}</p>

                  <p className="text-gray-600 dark:text-gray-400 mb-4">Capacity: {skip.capacity}</p>
                  <h4 className="font-medium mb-2 dark:text-white">Suitable for:</h4>
                  <ul className="space-y-2">
                    {skip.suitableFor.map((use, index) => (
                      <li key={index} className="flex items-center text-gray-600 dark:text-gray-400">
                        <Check size={16} className="text-[#ffb000] mr-2" />
                        {use}
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handleGetQuote(skip.id)}
                    className="w-full mt-6 bg-[#ffb000] text-white px-6 py-2 rounded-md font-medium hover:bg-[#e69d00] transition-colors"
                  >
                    Starting from £{skip.price.toFixed(2)}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 dark:bg-gray-900 transition-colors">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold mb-6 dark:text-white">Get in Touch</h2>
              <ContactForm />
            </div>

            {/* Service Area Checker */}
            <div>
              <h2 className="text-3xl font-bold mb-6 dark:text-white">Check Your Area</h2>
              <div className="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Enter your postcode to check if we service your area and get an instant quote.
                </p>
                <PostcodeChecker />
              </div>
              <div className="mt-8">
                <Map />
              </div>
            </div>
          </div>
        </div>
      </section>
      <QuoteModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
        selectedSkipId={selectedSkipId}
      />
    </div>
  );
};

export default HomePage;