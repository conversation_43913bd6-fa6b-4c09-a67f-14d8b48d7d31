import { Truck, Recycle, Clock, Shield } from 'lucide-react';

const features = [
  {
    icon: <Truck className="w-10 h-10 text-[#ffb000]" />,
    title: 'Next Day Delivery',
    description: 'Quick and efficient delivery to your location',
  },
  {
    icon: <Recycle className="w-10 h-10 text-[#ffb000]" />,
    title: 'Eco-Friendly',
    description: '90% of waste recycled',
  },
  {
    icon: <Clock className="w-10 h-10 text-[#ffb000]" />,
    title: '24/7 Support',
    description: 'Always here when you need us',
  },
  {
    icon: <Shield className="w-10 h-10 text-[#ffb000]" />,
    title: 'Licensed & Insured',
    description: 'Fully compliant with regulations',
  },
];

const Features = () => {
  return (
    <section className="py-16 bg-white dark:bg-gray-900 transition-colors">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6">
              <div className="flex justify-center mb-4">{feature.icon}</div>
              <h3 className="text-lg font-semibold mb-2 dark:text-white">{feature.title}</h3>
              <p className="text-gray-600 dark:text-gray-400">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;