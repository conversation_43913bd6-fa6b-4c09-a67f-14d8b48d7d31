import { APIProvider, Map as <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@vis.gl/react-google-maps';

const locations = [
  { name: 'Manchester', lat: 53.4808, lng: -2.2426 },
  { name: 'Salford', lat: 53.4830, lng: -2.2931 },
  { name: 'Bramhall', lat: 53.3580, lng: -2.1654 },
  { name: 'Alderley Edge', lat: 53.3039, lng: -2.2373 },
  { name: 'Heald Green', lat: 53.3690, lng: -2.2364 },
  { name: 'Handforth', lat: 53.3463, lng: -2.2135 },
  { name: 'Prest<PERSON>', lat: 53.2928, lng: -2.1455 },
  { name: 'Hazel Grove', lat: 53.3763, lng: -2.1162 },
  { name: 'Macclesfield', lat: 53.2580, lng: -2.1274 },
  { name: 'Knutsford', lat: 53.3028, lng: -2.3748 },
  { name: '<PERSON><PERSON>', lat: 53.5200, lng: -2.1800 },
  { name: '<PERSON><PERSON>', lat: 53.5300, lng: -2.2200 },
  { name: '<PERSON><PERSON><PERSON>', lat: 53.5100, lng: -2.1600 },
  { name: 'Middleton', lat: 53.5500, lng: -2.2000 },
  { name: 'Chadderton', lat: 53.5400, lng: -2.1500 },
  { name: 'Prestwich', lat: 53.5300, lng: -2.2900 },
  { name: 'Whitefield', lat: 53.5500, lng: -2.3000 },
  { name: 'Denton', lat: 53.4500, lng: -2.1200 },
  { name: 'Ashton', lat: 53.4900, lng: -2.1000 },
  { name: 'Eccles', lat: 53.4800, lng: -2.3400 },
  { name: 'Openshaw', lat: 53.4700, lng: -2.1800 },
  { name: 'Longsight', lat: 53.4500, lng: -2.2000 },
  { name: 'Didsbury', lat: 53.4166, lng: -2.2315 },
  { name: 'Whittington', lat: 53.2727, lng: -2.1564 },
  { name: 'Northenden', lat: 53.4056, lng: -2.2482 },
  { name: 'West Didsbury', lat: 53.4210, lng: -2.2387 },
  { name: 'Bowden', lat: 53.3789, lng: -2.3653 },
  { name: 'Dunham Massey', lat: 53.3800, lng: -2.4100 },
  { name: 'Little Bollington', lat: 53.3709, lng: -2.4023 },
  { name: 'Carrington', lat: 53.4231, lng: -2.3921 },
  { name: 'Partington', lat: 53.4183, lng: -2.4333 },
  { name: 'Warrington', lat: 53.3926, lng: -2.5931 },
  { name: 'Culcheth', lat: 53.4515, lng: -2.5246 },
  { name: 'Lymm', lat: 53.3814, lng: -2.4791 },
  { name: 'Birchwood', lat: 53.4220, lng: -2.5062 },
  { name: 'Woolston', lat: 53.4001, lng: -2.5325 },
  { name: 'Golborne', lat: 53.4760, lng: -2.5980 },
  { name: 'Hindley', lat: 53.5318, lng: -2.5794 },
  { name: 'Westhoughton', lat: 53.5480, lng: -2.5280 },
  { name: 'Tyldesley', lat: 53.5120, lng: -2.4660 },
  { name: 'Atherton', lat: 53.5230, lng: -2.4930 },
  { name: 'Walkden', lat: 53.5200, lng: -2.3980 },
  { name: 'Lostock', lat: 53.5780, lng: -2.4990 },
  { name: 'Middlebrook', lat: 53.5800, lng: -2.5350 },
  { name: 'Little Lever', lat: 53.5700, lng: -2.3700 },
  { name: 'Radcliffe', lat: 53.5600, lng: -2.3100 },
  { name: 'Whitfield', lat: 53.4700, lng: -1.9500 },
  { name: 'Tottington', lat: 53.6200, lng: -2.3200 },
  { name: 'Heywood', lat: 53.5920, lng: -2.2200 },
  { name: 'Rochdale', lat: 53.6164, lng: -2.1556 },
  { name: 'Rhodes', lat: 53.5330, lng: -2.1950 },
  { name: 'Hopwood', lat: 53.5820, lng: -2.1810 },
  { name: 'Royton', lat: 53.5650, lng: -2.1250 },
  { name: 'Shaw', lat: 53.5780, lng: -2.0900 },
  { name: 'Lees', lat: 53.5430, lng: -2.0650 },
  { name: 'Mossley', lat: 53.5140, lng: -2.0410 },
  { name: 'Stalybridge', lat: 53.4840, lng: -2.0510 },
  { name: 'Dukinfield', lat: 53.4740, lng: -2.0970 },
  { name: 'Adlington', lat: 53.3070, lng: -2.0850 },
  { name: 'Woodford', lat: 53.3400, lng: -2.1560 },
  { name: 'Glossop', lat: 53.4440, lng: -1.9490 },
  { name: 'Uppermill', lat: 53.5550, lng: -2.0050 },
  { name: 'Stubbins', lat: 53.6440, lng: -2.3150 },
  { name: 'Haslingden', lat: 53.7050, lng: -2.3270 },
  { name: 'Accrington', lat: 53.7530, lng: -2.3700 },
  { name: 'Leyland', lat: 53.6970, lng: -2.6900 },
  { name: 'Eccleston', lat: 53.6530, lng: -2.7390 },
  { name: 'Bamber Bridge', lat: 53.7250, lng: -2.6610 },
  { name: 'Freckleton', lat: 53.7570, lng: -2.8670 },
  { name: 'Kirkham', lat: 53.7820, lng: -2.8720 },
  { name: 'Crewe', lat: 53.0990, lng: -2.4410 },
  { name: 'Stafford', lat: 52.8050, lng: -2.1160 }
];

const center = {
  lat: 53.3580,
  lng: -2.2135,
};

const Map = () => {
  return (
    <APIProvider apiKey="AIzaSyCGPeYw9ifCZK87iLmYWcQVV2eNdR3ned8">
      <GoogleMap
        style={{ width: '100%', height: '300px', borderRadius: '8px' }}
        defaultCenter={center}
        defaultZoom={11}
      >
        {locations.map((location) => (
          <Marker
            key={location.name}
            position={{ lat: location.lat, lng: location.lng }}
            title={location.name}
          />
        ))}
      </GoogleMap>
    </APIProvider>
  );
};

export default Map;