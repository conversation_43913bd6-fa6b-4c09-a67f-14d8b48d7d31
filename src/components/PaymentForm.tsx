import React, { useState } from 'react';
import {
  Elements,
  useStripe,
  useElements,
  CardElement
} from '@stripe/react-stripe-js';
import { getStripe } from '../services/stripe';

interface PaymentFormContentProps {
  amount: number;
  onSuccess: () => void;
  onError: (error: string) => void;
}

const PaymentFormContent: React.FC<PaymentFormContentProps> = ({
  amount,
  onSuccess,
  onError
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
      });

      if (error) {
        onError(error.message || 'An error occurred during payment.');
      } else {
        onSuccess();
      }
    } catch (err) {
      onError(err instanceof Error ? err.message : 'An unexpected error occurred');
    }

    setIsProcessing(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="p-4 border rounded-md bg-white">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
              invalid: {
                color: '#9e2146',
              },
            },
          }}
        />
      </div>
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className="w-full bg-[#ffb000] text-white px-6 py-3 rounded-md font-medium hover:bg-[#e69d00] transition-colors disabled:opacity-50"
      >
        {isProcessing ? 'Processing...' : `Pay £${amount.toFixed(2)}`}
      </button>
    </form>
  );
}

const PaymentForm: React.FC<PaymentFormContentProps> = (props) => {
  return (
    <Elements stripe={getStripe()} options={{
      appearance: {
        theme: 'stripe',
        variables: {
          colorPrimary: '#ffb000',
        },
      },
      currency: 'gbp',
      mode: 'payment',
      amount: Math.round(props.amount * 100), // Convert to pence
    }}>
      <PaymentFormContent {...props} />
    </Elements>
  );
};

export default PaymentForm;