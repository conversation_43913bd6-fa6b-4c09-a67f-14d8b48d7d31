// PostcodeChecker.jsx
import { useState } from 'react';
import { Search } from 'lucide-react';

// List of all serviceable postcode districts (outward codes)
const serviceablePostcodes = new Set([
  // Manchester and surrounding areas
  'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M8', 'M9', 'M11', 'M12', 'M13',
  'M14', 'M15', 'M16', 'M18', 'M19', 'M20', 'M21', 'M22', 'M23', 'M24', 'M25',
  'M26', 'M27', 'M28', 'M29', 'M30', 'M31', 'M32', 'M33', 'M34', 'M35', 'M38',
  'M40', 'M41', 'M43', 'M44', 'M45', 'M46', 'M50',
  
  // Stockport and Cheshire areas
  'SK1', 'SK2', 'SK3', 'SK4', 'SK5', 'SK6', 'SK7', 'SK8', 'SK9', 'SK10',
  'SK11', 'SK12', 'SK13', 'SK14', 'SK15', 'SK16',
  
  // Oldham and Rochdale
  'OL1', 'OL2', 'OL3', 'OL4', 'OL5', 'OL6', 'OL7', 'OL8', 'OL9', 'OL10',
  'OL11', 'OL12', 'OL13', 'OL14', 'OL15', 'OL16',
  
  // Bolton and Bury
  'BL0', 'BL1', 'BL2', 'BL3', 'BL4', 'BL5', 'BL6', 'BL7', 'BL8', 'BL9',
  
  // Warrington and surrounding areas
  'WA1', 'WA2', 'WA3', 'WA4', 'WA5', 'WA13', 'WA14', 'WA15', 'WA16',
  
  // Preston and surrounding areas
  'PR1', 'PR2', 'PR3', 'PR4', 'PR5', 'PR25', 'PR26',
  
  // Crewe and Stafford
  'CW1', 'CW2', 'ST16', 'ST17', 'ST18'
]);

const isValidPostcode = (postcode: string): boolean => {
  // Basic UK postcode validation (without space)
  const regex = /^[A-Z]{1,2}\d[A-Z\d]?\d[A-Z]{2}$/;
  return regex.test(postcode);
};

const PostcodeChecker = () => {
  const [postcode, setPostcode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleCheck = () => {
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const normalized = postcode.replace(/\s/g, '').toUpperCase();
      
      if (!isValidPostcode(normalized)) {
        alert('Please enter a valid UK postcode');
        setIsLoading(false);
        return;
      }

      const outwardCode = normalized.slice(0, -3);
      const isServiced = serviceablePostcodes.has(outwardCode);

      if (isServiced) {
        alert('Service available in your area! Please proceed with your quote.');
      } else {
        const message = 'While we don\'t currently offer online booking for your area, ' +
          'Please fill out our contact form or call our office directly for a custom quote. ' +
          'We may still be able to accommodate your requirements.';
        alert(message);
      }

      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="w-full max-w-md">
      <div className="relative">
        <input
          type="text"
          value={postcode}
          onChange={(e) => setPostcode(e.target.value)}
          placeholder="Enter your postcode"
          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent"
          required
        />
        <button
          type="button"
          onClick={handleCheck}
          disabled={isLoading}
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-[#ffb000] text-white p-2 rounded-md hover:bg-[#e69d00] transition-colors disabled:opacity-50"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Search size={20} />
          )}
        </button>
      </div>
    </div>
  );
};

export default PostcodeChecker;