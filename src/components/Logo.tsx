import { useTheme } from '../context/ThemeContext';

interface LogoProps {
  variant?: 'auto' | 'white';
}

const Logo = ({ variant = 'auto' }: LogoProps) => {
  const { isDark } = useTheme();

  let logoSource;
  if (variant === 'white') {
    logoSource = '/assets/images/logo-white.png';
  } else {
    logoSource = isDark 
      ? '/assets/images/logo-white.png' 
      : '/assets/images/logo.png';
  }

  return (
    <div className="flex items-center space-x-2">
      <img
        src={logoSource}
        alt="National Skip Hire UK Logo"
        className="h-12"
      />
    </div>
  );
};

export default Logo;