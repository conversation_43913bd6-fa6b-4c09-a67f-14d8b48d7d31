import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import Logo from './Logo';
import { useState } from 'react';
import LegalModal from './LegalModal';

const Footer = () => {
  const [isPrivacyOpen, setIsPrivacyOpen] = useState(false);
  const [isTermsOpen, setIsTermsOpen] = useState(false);

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Logo variant="white" />
            <p className="text-gray-400">
              Your trusted partner in waste management and skip hire services across the UK.
            </p>
            {/* 
            <div className="flex space-x-4">
              <Facebook className="w-5 h-5 text-gray-400 hover:text-[#ffb000] cursor-pointer" />
              <Twitter className="w-5 h-5 text-gray-400 hover:text-[#ffb000] cursor-pointer" />
              <Instagram className="w-5 h-5 text-gray-400 hover:text-[#ffb000] cursor-pointer" />
              <Linkedin className="w-5 h-5 text-gray-400 hover:text-[#ffb000] cursor-pointer" />
            </div>
            */}
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#services" className="text-gray-400 hover:text-[#ffb000]">
                  Our Services
                </a>
              </li>
              <li>
                <a href="#/about" className="text-gray-400 hover:text-[#ffb000]">
                  About Us
                </a>
              </li>
              <li>
                <a href="#contact" className="text-gray-400 hover:text-[#ffb000]">
                  Contact
                </a>
              </li>
              <li>
                <a href="#/blog" className="text-gray-400 hover:text-[#ffb000]">
                  Blog
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li className="text-gray-400">Domestic Skip Hire</li>
              <li className="text-gray-400">Commercial Skip Hire</li>
              <li className="text-gray-400">Construction Waste</li>
              <li className="text-gray-400">Recycling Services</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-[#ffb000]" />
                <span className="text-gray-400">0800 622 6644</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-[#ffb000]" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-[#ffb000]" />
                <span className="text-gray-400">Nationwide Coverage</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} National Skip Hire UK Ltd. All rights reserved.
            </p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <button
                onClick={() => setIsPrivacyOpen(true)}
                className="text-gray-400 hover:text-[#ffb000] text-sm"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => setIsTermsOpen(true)}
                className="text-gray-400 hover:text-[#ffb000] text-sm"
              >
                Terms & Conditions
              </button>
            </div>
          </div>
        </div>
      </div>
      <LegalModal
        isOpen={isPrivacyOpen}
        onClose={() => setIsPrivacyOpen(false)}
        type="privacy"
      />
      <LegalModal
        isOpen={isTermsOpen}
        onClose={() => setIsTermsOpen(false)}
        type="terms"
      />
    </footer>
  );
};

export default Footer;