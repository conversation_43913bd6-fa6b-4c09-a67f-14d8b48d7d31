import React, { useState, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Menu, X, Phone } from 'lucide-react';
import Logo from './Logo';
import ThemeToggle from './ThemeToggle';
import QuoteModal from './QuoteModal';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const scrollToSection = useCallback((e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    if (location.pathname !== '/') {
      navigate('/');
      setTimeout(() => {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    } else {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
        setIsMenuOpen(false);
      }
    }
  }, [location, navigate]);

  const navLinks = [
    { href: '#home', label: 'Home' },
    { href: '#services', label: 'Services' },
    { href: '#/about', label: 'FAQs', isPage: true },
    { href: '#/blog', label: 'Blog', isPage: true },
    { href: '#contact', label: 'Contact' }
  ];

  return (
    <header className="fixed w-full bg-white dark:bg-gray-900 shadow-md z-50 transition-colors">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Logo variant="auto"/>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map(({ href, label, isPage }) => (
              isPage ? (
                <Link
                  key={href}
                  to={href.replace('#', '')}
                  className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-colors hover:text-[#ffb000]"
                >
                  {label}
                </Link>
              ) : (
                <a
                  key={href}
                  href={href}
                  onClick={(e) => scrollToSection(e, href)}
                  className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-colors hover:text-[#ffb000]"
                >
                  {label}
                </a>
              )
            ))}
            <a
              href="tel:0800-622-6644"
              className="flex items-center space-x-2 text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-[#ffb000]"
            >
              <Phone size={16} />
              <span>0800 622 6644</span>
            </a>
            <ThemeToggle />
            <button
              onClick={() => setIsQuoteModalOpen(true)}
              className="bg-[#ffb000] text-white px-6 py-2 rounded-md font-medium hover:bg-[#e69d00] transition-colors"
            >
              Get a Quote
            </button>
          </nav>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X size={24} className="text-black" />
            ) : (
              <Menu size={24} className="text-black" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4">
            <div className="flex flex-col space-y-4 items-start">
              {navLinks.map(({ href, label, isPage }) => (
                isPage ? (
                  <Link
                    key={href}
                    to={href.replace('#', '')}
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-colors hover:text-[#ffb000]"
                  >
                    {label}
                  </Link>
                ) : (
                  <a
                    key={href}
                    href={href}
                    onClick={(e) => scrollToSection(e, href)}
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 transition-colors hover:text-[#ffb000]"
                  >
                    {label}
                  </a>
                )
              ))}
              <a
                href="tel:0800-skip-hire"
                className="flex items-center space-x-2 text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-[#ffb000]"
              >
                <Phone size={16} />
                <span>0800 622 6644</span>
              </a>
              <div className="w-full flex justify-between items-center">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Theme</span>
                <ThemeToggle />
              </div>
              <button
                onClick={() => setIsQuoteModalOpen(true)}
                className="bg-[#ffb000] text-white px-6 py-2 rounded-md font-medium hover:bg-[#e69d00] transition-colors w-full"
              >
                Get a Quote
              </button>
            </div>
          </div>
        )}
      </div>
      <QuoteModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
      />
    </header>
  );
};

export default Header;