import React, { useState } from 'react';
import { X } from 'lucide-react';
import { skipSizes } from '../data/skips';
import { getPriceForPostcode, isValidDeliveryDate } from '../data/pricing';
import { redirectToCheckout } from '../services/stripe';

interface QuoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSkipId?: string;
}

const QuoteModal: React.FC<QuoteModalProps> = ({ isOpen, onClose, selectedSkipId }) => {
  const [formData, setFormData] = useState(() => ({
    name: '',
    email: '',
    phone: '',
    address: '',
    postcode: '',
    skipSize: selectedSkipId ?? '',
    startDate: '',
    endDate: '',
    additionalInfo: '',
    includePermit: true, // Default to true for permit
    extras: {
      mattress: false,
      sofa: false,
      fridge: false,
      plasterboard: false
    }
  }));

  // Track if we need to recalculate the price
  const [shouldRecalculate, setShouldRecalculate] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [result, setResult] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Price calculation state
  const [pricing, setPricing] = useState<{
    basePrice: number;
    permitCost: number;
    extrasCost: number;
    total: number;
    zoneName: string;
    extraDays: number;
    extraCharge: number;
  } | null>(null);

  // Update skipSize when selectedSkipId changes
  React.useEffect(() => {
    if (selectedSkipId) {
      setFormData(prev => ({
        ...prev,
        skipSize: selectedSkipId
      }));
      setShouldRecalculate(true);
    }
  }, [selectedSkipId]);

  // Effect to calculate price whenever relevant form data changes
  React.useEffect(() => {
    if (shouldRecalculate) {
      calculatePrice();
      setShouldRecalculate(false);
    }
  }, [shouldRecalculate]);

  // Effect to recalculate price when form data changes
  React.useEffect(() => {
    // Only calculate if we have the minimum required fields
    if (formData.postcode && formData.skipSize) {
      calculatePrice();
    }
  }, [
    formData.postcode,
    formData.skipSize,
    formData.startDate,
    formData.endDate,
    formData.includePermit,
    formData.extras.mattress,
    formData.extras.sofa,
    formData.extras.fridge,
    formData.extras.plasterboard
  ]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const name = e.target.name;
    const value = e.target.type === 'checkbox'
      ? (e.target as HTMLInputElement).checked
      : e.target.value;

    // Special handling for date validation
    if (name === 'startDate') {
      const selectedDate = new Date(value as string);
      if (!isValidDeliveryDate(selectedDate)) {
        setErrors(['Please select a valid delivery date (48 hours notice, no weekends or bank holidays)']);
      } else {
        setErrors([]);
      }
    }

    // Update form data
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleExtraChange = (extra: keyof typeof formData.extras) => {
    setFormData(prev => ({
      ...prev,
      extras: {
        ...prev.extras,
        [extra]: !prev.extras[extra]
      },
    }));
  };

  const calculatePrice = () => {
    // Don't calculate if we don't have the minimum required fields
    if (!formData.postcode || !formData.skipSize) {
      setPricing(null);
      return;
    }

    try {
      const priceZone = getPriceForPostcode(formData.postcode);
      if (!priceZone) {
        setPricing(null);
        return;
      }

      const skipSize = skipSizes.find(skip => skip.id === formData.skipSize);
      if (!skipSize) {
        setPricing(null);
        return;
      }

      // Calculate days between start and end date
      let days = 0;
      if (formData.startDate && formData.endDate) {
        const startDate = new Date(formData.startDate);
        const endDate = new Date(formData.endDate);

        // Ensure valid dates
        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          days = Math.ceil(
            (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
          );
          // Ensure days is not negative
          days = Math.max(0, days);
        }
      }

      // Calculate extra days beyond the standard 7-day period
      const extraDays = Math.max(0, days - 7);
      const extraCharge = extraDays * 10;

      // Get base price for the selected skip size
      const basePrice = priceZone.skipPrices[skipSize.id] || 0;

      // Calculate permit cost based on user selection
      const permitCost = formData.includePermit ? priceZone.permitCost : 0;

      // Calculate extras cost
      let extrasCost = 0;
      if (formData.extras.mattress) extrasCost += 25;
      if (formData.extras.sofa) extrasCost += 25;
      if (formData.extras.fridge) extrasCost += 25;
      if (formData.extras.plasterboard) extrasCost += 180;

      // Make sure we have valid numbers for all components
      const validBasePrice = isNaN(basePrice) ? 0 : basePrice;
      const validPermitCost = isNaN(permitCost) ? 0 : permitCost;
      const validExtrasCost = isNaN(extrasCost) ? 0 : extrasCost;
      const validExtraCharge = isNaN(extraCharge) ? 0 : extraCharge;

      // Calculate total price
      const total = validBasePrice + validPermitCost + validExtrasCost + validExtraCharge;

      // Update pricing state
      setPricing({
        basePrice: validBasePrice,
        permitCost: validPermitCost,
        extrasCost: validExtrasCost,
        total,
        zoneName: priceZone.name,
        extraDays,
        extraCharge: validExtraCharge
      });
    } catch (error) {
      console.error('Error calculating price:', error);
      setPricing(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: string[] = [];

    // Force price calculation when the form is submitted
    // This ensures the price is calculated even when coming from the Header button
    if (!pricing) {
      calculatePrice();

      // If we don't have pricing yet, just return - the user needs to click Calculate Quote again
      if (!formData.postcode || !formData.skipSize) {
        if (!formData.postcode) {
          newErrors.push('Please enter a valid postcode');
        }
        if (!formData.skipSize) {
          newErrors.push('Please select a skip size');
        }
        setErrors(newErrors);
        return;
      }

      // If we have all required fields but still no pricing, try to calculate again
      calculatePrice();

      // If still no pricing after calculation, show an error
      if (!pricing) {
        setErrors(['Unable to calculate price. Please check your postcode and try again.']);
        return;
      }
    }

    // Validate delivery date
    if (formData.startDate) {
      const startDate = new Date(formData.startDate);
      if (!isValidDeliveryDate(startDate)) {
        newErrors.push('Invalid delivery date. Please note: 48 hours notice required, no weekends or bank holidays.');
      }
    } else {
      newErrors.push('Please select a delivery date');
    }

    // Validate postcode and pricing
    if (!pricing) {
      newErrors.push('Service not available in your area or invalid skip size selected.');
    }

    if (newErrors.length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);
    setResult('Sending...');

    const formDataToSend = new FormData();

    // Add form fields and pricing information
    const skipSize = skipSizes.find(skip => skip.id === formData.skipSize);

    formDataToSend.append('name', formData.name);
    formDataToSend.append('email', formData.email);
    formDataToSend.append('phone', formData.phone);
    formDataToSend.append('address', formData.address);
    formDataToSend.append('postcode', formData.postcode);
    formDataToSend.append('skipSize', skipSize?.name || '');
    formDataToSend.append('startDate', formData.startDate);
    formDataToSend.append('endDate', formData.endDate);
    formDataToSend.append('additionalInfo', formData.additionalInfo);

    // Add extras information
    if (formData.extras.mattress) formDataToSend.append('extras[]', 'Mattress');
    if (formData.extras.sofa) formDataToSend.append('extras[]', 'Sofa');
    if (formData.extras.fridge) formDataToSend.append('extras[]', 'Fridge');
    if (formData.extras.plasterboard) formDataToSend.append('extras[]', 'Plasterboard');

    // Add pricing information
    if (pricing) {
      formDataToSend.append('zone', pricing.zoneName);
      formDataToSend.append('basePrice', `£${pricing.basePrice}`);
      formDataToSend.append('permitCost', `£${pricing.permitCost}`);
      formDataToSend.append('extrasCost', `£${pricing.extrasCost}`);
      formDataToSend.append('totalPrice', `£${pricing.total}`);
    }

    // Add your access key
    formDataToSend.append('access_key', 'a4781b33-9b21-4bdd-93ec-9ada27e0e014');

    // Add form name for identification in emails
    formDataToSend.append('from_name', 'Skip Hire Quote Request');
    formDataToSend.append('subject', `Skip Hire Quote: ${formData.name} - ${pricing?.zoneName}`);

    try {
      const response = await fetch('https://api.web3forms.com/submit', {
        method: 'POST',
        body: formDataToSend
      });

      const data = await response.json();

      if (data.success) {
        try {
          await redirectToCheckout({
            skipSize: skipSize?.name || '',
            price: pricing.total,
            customerEmail: formData.email,
            startDate: formData.startDate,
            endDate: formData.endDate,
          });
        } catch (error) {
          console.error('Checkout error:', error);
          setResult('Error setting up payment. Please try again.');
          return;
        }
      } else {
        setResult('Something went wrong. Please try again later.');
      }
    } catch (error) {
      console.error('Error:', error);
      setResult('Something went wrong. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 modal-overlay ${
        isOpen ? 'modal-overlay-visible' : ''
      }`}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div
        className={`bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto modal-content ${
          isOpen ? 'modal-content-visible' : ''
        }`}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold dark:text-white">Get a Quote</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X size={24} />
            </button>
          </div>

          {errors.length > 0 && (
            <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <ul className="list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="postcode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Postcode
                </label>
                <input
                  type="text"
                  id="postcode"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="skipSize" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Skip Size
                </label>
                <select
                  id="skipSize"
                  name="skipSize"
                  value={formData.skipSize}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                >
                  <option value="">Select a skip size</option>
                  {skipSizes.map((skip) => (
                    <option key={skip.id} value={skip.id}>
                      {skip.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  required
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  required
                  min={formData.startDate || new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Permit Option */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium dark:text-white">Permit Options</h3>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="includePermit"
                  checked={formData.includePermit}
                  onChange={handleChange}
                  className="form-checkbox h-5 w-5 text-[#ffb000]"
                />
                <span className="dark:text-white">Include Council Permit (required if skip is placed on public road)</span>
              </label>
            </div>

            {/* Extras Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium dark:text-white">Additional Items</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="extras.mattress"
                    checked={formData.extras.mattress}
                    onChange={() => handleExtraChange('mattress')}
                    className="form-checkbox h-5 w-5 text-[#ffb000]"
                  />
                  <span className="dark:text-white">Mattress (£25)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="extras.sofa"
                    checked={formData.extras.sofa}
                    onChange={() => handleExtraChange('sofa')}
                    className="form-checkbox h-5 w-5 text-[#ffb000]"
                  />
                  <span className="dark:text-white">Sofa (£25)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="extras.fridge"
                    checked={formData.extras.fridge}
                    onChange={() => handleExtraChange('fridge')}
                    className="form-checkbox h-5 w-5 text-[#ffb000]"
                  />
                  <span className="dark:text-white">Fridge (£25)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    name="extras.plasterboard"
                    checked={formData.extras.plasterboard}
                    onChange={() => handleExtraChange('plasterboard')}
                    className="form-checkbox h-5 w-5 text-[#ffb000]"
                  />
                  <span className="dark:text-white">Plasterboard (£180)</span>
                </label>
              </div>
            </div>

            {/* Price Breakdown */}
            {pricing && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-2">
                <h3 className="text-lg font-medium dark:text-white">Price Breakdown</h3>
                <div className="flex justify-between">
                  <span className="dark:text-gray-300">Hire Duration</span>
                  <span className="font-medium dark:text-white">
                    {formData.startDate && formData.endDate ?
                      (() => {
                        const startDate = new Date(formData.startDate);
                        const endDate = new Date(formData.endDate);
                        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                          return 'Not set';
                        }
                        const days = Math.max(0, Math.ceil(
                          (endDate.getTime() - startDate.getTime())
                          / (1000 * 60 * 60 * 24)
                        ));
                        return `${days} days`;
                      })() : 'Not set'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="dark:text-gray-300">Skip Hire</span>
                  <span className="font-medium dark:text-white">£{pricing.basePrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="dark:text-gray-300">Permit Cost</span>
                  <span className="font-medium dark:text-white">£{pricing.permitCost.toFixed(2)}</span>
                </div>
                {pricing.extrasCost > 0 && (
                  <div className="flex justify-between">
                    <span className="dark:text-gray-300">Additional Items</span>
                    <span className="font-medium dark:text-white">£{pricing.extrasCost.toFixed(2)}</span>
                  </div>
                )}
                {pricing.extraDays > 0 && (
                  <div className="flex justify-between">
                    <span className="dark:text-gray-300">Extended Hire ({pricing.extraDays} days @ £10/day)</span>
                    <span className="font-medium dark:text-white">£{pricing.extraCharge.toFixed(2)}</span>
                  </div>
                )}
                <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                  <div className="flex justify-between">
                    <span className="font-medium dark:text-white">Total</span>
                    <span className="font-bold text-[#ffb000]">£{pricing.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="additionalInfo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Additional Information
              </label>
              <textarea
                id="additionalInfo"
                name="additionalInfo"
                value={formData.additionalInfo}
                onChange={handleChange}
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ffb000] focus:border-transparent dark:bg-gray-900 dark:text-white"
                placeholder="Please provide any additional details about your requirements..."
              />
            </div>

            {/* Error messages are now shown only at the top of the form */}

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-[#ffb000] text-white px-6 py-3 rounded-md font-medium hover:bg-[#e69d00] transition-colors disabled:opacity-50"
            >
              {isSubmitting ? 'Processing...' : pricing ? `Proceed to Payment (£${pricing.total})` : 'Calculate Quote'}
            </button>

            {result && (
              <div className={`text-center p-3 rounded ${
                result.includes('successful') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                {result}
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default QuoteModal;