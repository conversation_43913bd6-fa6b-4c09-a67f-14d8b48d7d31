# Setting Up Environment Variables with Nginx

## 1. Place your .env file in the /private directory

Upload your `.env` file to the `/private` directory on your server. This directory is not publicly accessible, which is perfect for sensitive information like API keys.

## 2. Configure Nginx to pass environment variables to your application

You'll need to modify your Nginx configuration to read the environment variables from the `.env` file and pass them to your application.

### Option 1: Using Nginx environment variables

Add this to your Nginx server block configuration:

```nginx
server {
    # Your existing configuration...
    
    location / {
        # Your existing location configuration...
        
        # Include environment variables
        include /private/.env;
    }
}
```

### Option 3: Using a server-side script to load environment variables

If you're using a server-side language like Node.js, you can load the environment variables directly in your application:

```javascript
// In your server.js or similar file
require('dotenv').config({ path: '/private/.env' });
```

## 3. Restart Nginx after making changes

```bash
sudo systemctl restart nginx
```

## Security Considerations

1. Make sure the `/private` directory has restricted permissions:
   ```bash
   chmod 700 /private
   chmod 600 /private/.env
   ```

2. Ensure the user running Nginx has read access to the `.env` file but no one else does.

3. Regularly rotate your API keys and update the `.env` file accordingly.
