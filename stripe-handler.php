<?php
// This file contains the actual Stripe logic
// Place this file in the /private directory

// Stripe API keys - replace with your actual keys
$stripeSecretKey = '***********************************************************************************************************';

// Get the request body
$input = file_get_contents('php://input');
$requestData = json_decode($input, true);

// Validate request data
if (!$requestData || !isset($requestData['amount']) || !isset($requestData['skipSize']) || 
    !isset($requestData['startDate']) || !isset($requestData['endDate'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

// Extract data from request
$amount = $requestData['amount'];
$skipSize = $requestData['skipSize'];
$startDate = $requestData['startDate'];
$endDate = $requestData['endDate'];
$customerEmail = isset($requestData['customerEmail']) ? $requestData['customerEmail'] : null;

try {
    // Using cURL to create a checkout session
    $ch = curl_init();
    
    $data = [
        'payment_method_types' => ['card'],
        'line_items' => [[
            'price_data' => [
                'currency' => 'gbp',
                'product_data' => [
                    'name' => 'Skip Hire: ' . $skipSize,
                    'description' => 'Hire period: ' . $startDate . ' to ' . $endDate,
                ],
                'unit_amount' => round($amount * 100), // Convert to pence
            ],
            'quantity' => 1,
        ]],
        'mode' => 'payment',
        'success_url' => 'https://nationalskiphireuk.co.uk/payment-success',
        'cancel_url' => 'https://nationalskiphireuk.co.uk/payment-cancelled',
    ];
    
    // Add customer email if provided
    if ($customerEmail) {
        $data['customer_email'] = $customerEmail;
    }
    
    curl_setopt($ch, CURLOPT_URL, 'https://api.stripe.com/v1/checkout/sessions');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_USERPWD, $stripeSecretKey . ':');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if ($httpcode != 200) {
        throw new Exception('Error creating Stripe checkout session: ' . $response);
    }
    
    curl_close($ch);
    
    // Return the session ID to the client
    $responseData = json_decode($response, true);
    echo json_encode(['id' => $responseData['id']]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
