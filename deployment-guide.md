# Deployment Guide for National Skip Hire UK Website

This guide explains how to deploy your website to your Nginx server with the Stripe integration.

## Step 1: Build Your Application

1. Make sure all your changes are saved
2. Run the build command:
   ```bash
   npm run build
   ```
3. This will create a `dist` directory with your built application

## Step 2: Prepare Your Configuration File

1. Edit the `config.js` file in the `public` directory to include your Stripe public key:
   ```javascript
   window.APP_CONFIG = {
     STRIPE_PUBLIC_KEY: 'pk_live_51R3yV7ABiUSKf1XqIrHDc8kR20NCyF9NBxbvK9ygn3jjgrF7nhMP8sUND2RlfXyit0kTAuETzISVUIPyeIj38ObJ00LJj7DLLx'
   };
   ```
   
   Note: This file will be copied to the `dist` directory during the build process.

## Step 3: Upload Files to Your Nginx Server

1. Connect to your server using FTP or SFTP
2. Upload all files from the `dist` directory to your `/web` directory on the server
3. Make sure the `config.js` file is in the root of your `/web` directory

## Step 4: Test Your Application

1. Visit your website in a browser
2. Test the Stripe integration by going through the quote and payment process
3. Check the browser console for any errors related to Stripe configuration

## Important Security Notes

1. **Never include your Stripe secret key in your frontend code or config.js file**
2. The secret key should only be used in secure backend environments
3. For server-side operations that require the secret key, you'll need a separate backend service

## Troubleshooting

If you encounter issues with the Stripe integration:

1. Check the browser console for errors
2. Verify that the `config.js` file is being loaded (you should see it in the Network tab of your browser's developer tools)
3. Confirm that the Stripe public key is correctly set in the `config.js` file
4. Make sure the `config.js` file is accessible at the URL: `https://yourwebsite.com/config.js`

## Alternative Approach: Environment Variables at Build Time

If the runtime configuration approach doesn't work, you can use environment variables at build time:

1. Create a `.env.production` file in your project root:
   ```
   VITE_STRIPE_PUBLIC_KEY=pk_live_51R3yV7ABiUSKf1XqIrHDc8kR20NCyF9NBxbvK9ygn3jjgrF7nhMP8sUND2RlfXyit0kTAuETzISVUIPyeIj38ObJ00LJj7DLLx
   ```

2. Build your application:
   ```bash
   npm run build
   ```

3. Upload the contents of the `dist` directory to your `/web` directory on the server

This approach embeds the environment variables directly into your JavaScript files during the build process.
